# NOLK KPI Definitions

This document provides detailed definitions for all Key Performance Indicators (KPIs) used in the NOLK platform. These KPIs are used to measure and track the performance of brands across various dimensions.

## Table of Contents

1. [General KPIs](#general-kpis)
2. [Amazon-Specific KPIs](#amazon-specific-kpis)
3. [KPI Display and Formatting](#kpi-display-and-formatting)
4. [KPI Categories](#kpi-categories)

## General KPIs

### Gross Revenue
**Definition**: Total sales revenue before any deductions (discounts, refunds, taxes, shipping, etc.).  
**Formula**: Gross Revenue = Units Sold × Selling Price per Unit

### Discount
**Definition**: Total value of promotional discounts or coupons applied to orders.  
**Formula**: Discount = Sum of Discounts Applied

### Refunds
**Definition**: Total value of refunded transactions (including full and partial refunds).  
**Formula**: Refunds = Sum of Refunded Order Amounts

### Shipping and Delivery Income
**Definition**: Total shipping fees collected from customers.  
**Formula**: Shipping Income = Sum of Customer-Paid Shipping Fees

### Net Revenue
**Definition**: Revenue after accounting for discounts, refunds, and excluding taxes.  
**Formula**: Net Revenue = Gross Revenue - Discounts - Refunds + Shipping and Delivery Income

### Landed Cost
**Definition**: Total cost to acquire a product, including manufacturing, freight, duties, and handling.  
**Formula**: Landed Cost = Product Cost + Freight + Customs Duties + Insurance

### Fulfillment Fees
**Definition**: Costs related to storing, picking, packing, and shipping the products (e.g., 3PL or FBA fees).  
**Formula**: Fulfillment Fees = Warehousing Costs + Pick & Pack + Shipping Fees (to customer)

### Transaction Fees
**Definition**: Payment processing and marketplace transaction costs (e.g., Stripe, Shopify, Amazon referral fees).  
**Formula**: Transaction Fees = % of Sales + Flat Fees per Order

### Gross Margin
**Definition**: Revenue retained after accounting for product costs (before fulfillment, ads, etc.).  
**Formula**: Gross Margin = (Net Revenue - Landed Cost) / Net Revenue

### Contribution Margin
**Definition**: Profitability after accounting for all variable costs (landed cost, fulfillment, transaction fees, ad spend).  
**Formula**: Contribution Margin = (Net Revenue - Landed Cost - Fulfillment Fees - Transaction Fees - Adspend) / Net Revenue

### Adspend
**Definition**: Total advertising spend across all channels (Meta, Google, Amazon, etc.).  
**Formula**: Adspend = Sum of Paid Media Costs

## Amazon-Specific KPIs

### In-stock Rate
**Definition**: Percentage of time your products are available for purchase (not out of stock).  
**Formula**: In-stock Rate = (Time In-Stock / Total Time) × 100

### Excess Inventory Units
**Definition**: Number of units held in Amazon warehouses exceeding recommended levels (risk of long-term storage fees).  
**Formula**: Excess Inventory = Current FBA Stock - Forecasted 90-Day Demand

### Net Revenue on Amazon
**Definition**: Final Amazon revenue after discounts, including shipping income but before fees.  
**Formula**: Amazon Net Revenue = Gross Revenue - Discounts + Shipping Income - Refunds

### Unbranded ACOS
**Definition**: Advertising Cost of Sale for non-branded (generic) keywords only.  
**Formula**: Unbranded ACOS = Ad Spend on Unbranded Keywords / Attributed Sales from Unbranded Keywords

### Branded ACOS
**Definition**: ACOS for branded (your company/product name) keywords only.  
**Formula**: Branded ACOS = Ad Spend on Branded Keywords / Attributed Sales from Branded Keywords

### TACOS
**Definition**: Ratio of total ad spend to total revenue (gives a view of ad efficiency across all sales).  
**Formula**: TACOS = Total Ad Spend / Total Revenue

## KPI Display and Formatting

KPIs are displayed in the dashboard with appropriate formatting based on their type:

### Currency Formatting
- Revenue metrics (Gross Revenue, Net Revenue, etc.)
- Cost metrics (Landed Cost, Fulfillment Fees, etc.)
- Adspend
- AOV (Average Order Value)
- LTV (Lifetime Value)

These are formatted with currency symbols ($ or CA$) and use appropriate abbreviations for large numbers (k for thousands, M for millions).

### Percentage Formatting
- Gross Margin
- Contribution Margin
- ACOS
- TACOS
- Conversion Rate
- In-stock Rate

These are displayed with a percentage symbol (%) and typically show 1-2 decimal places.

### Count Formatting
- Orders
- Units Sold
- New Customers
- Repeat Customers
- Excess Inventory Units

These are displayed as whole numbers, with abbreviations (k, M) for large values.

## KPI Categories

KPIs can be categorized into the following groups:

### Revenue Metrics
- Gross Revenue
- Net Revenue
- Net Revenue on Amazon
- Shipping and Delivery Income

### Cost Metrics
- Landed Cost
- Fulfillment Fees
- Transaction Fees
- Adspend

### Margin Metrics
- Gross Margin
- Contribution Margin

### Order Metrics
- Orders
- Units Sold
- AOV (Average Order Value)

### Customer Metrics
- New Customers
- Repeat Customers
- LTV (Lifetime Value)

### Advertising Metrics
- Adspend
- ACOS (Advertising Cost of Sale)
- TACOS (Total Advertising Cost of Sale)
- Branded ACOS
- Unbranded ACOS

### Inventory Metrics
- In-stock Rate
- Excess Inventory Units

## KPI Analysis Dimensions

KPIs can be analyzed across multiple dimensions:

- **Time**: Day, Week, Month, Quarter, Year
- **Brand**: Individual brands within the NOLK portfolio
- **Sales Channel**: Direct-to-Consumer (D2C), Amazon, Wholesale, etc.
- **Geography**: Country, Region
- **Currency**: CAD, USD
