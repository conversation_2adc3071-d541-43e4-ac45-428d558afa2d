"use client";

import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { useEffect, useState } from "react";
import { ArrowLeft, Calendar, Target, TrendingUp, AlertCircle } from "lucide-react";

import { AppSidebar } from "@/components/app-sidebar";
import { CampaignInfo } from "@/components/campaign-detail/CampaignInfo";
import React from "react";
import { SiteHeader } from "@/components/site-header";
import { Skeleton } from "@/components/ui/skeleton";
import { SpendBreakdown } from "@/components/campaign-detail/SpendBreakdown";
import { SummaryCards } from "@/components/campaign-detail/SummaryCards";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import { formatCampaignName } from "@/lib/chart-utils";

// Define the type for CampaignDetails
export interface CampaignDetails {
  id: string;
  name: string;
  brand_name?: string;
  status?: string;
  budget?: number | null;
  spend: number | null;
  impressions?: number | null;
  clicks?: number | null;
  ctr?: string | null;
  cpc?: string | null;
  conversions?: number | null;
  cpa?: string | null;
  roas?: number | null;
  startDate?: string | null;
  endDate?: string | null;
  targetAudience?: string | null;
  channels?: string[] | null;
  notes?: string | null;
  FacebookSpend?: number | null;
  InstagramSpend?: number | null;
  GoogleSpend?: number | null;
  TikTokSpend?: number | null;
  AmazonSpend?: number | null;
  AwarenessSpend?: number | null;
  ConversionSpend?: number | null;
  RetargetingSpend?: number | null;
  SeasonalSpend?: number | null;
  totalConversionValue?: number | null;
  totalSpend?: number | null;
  dailyData?: Array<{
    date: string;
    impressions?: number;
    clicks?: number;
    spend?: number;
    conversions?: number;
    [key: string]: string | number | undefined;
  }>;
}

interface CampaignDetailsClientProps {
  params: Promise<{
    campaignId: string;
  }>;
}

export default function CampaignDetailsClient({ params }: CampaignDetailsClientProps) {
  // Use React.use to unwrap the params Promise before accessing properties
  const unwrappedParams = React.use(params);
  const campaignId = unwrappedParams.campaignId;
  const [campaignDetails, setCampaignDetails] = useState<CampaignDetails | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [defaultCurrency] = useState<string>("CAD");

  // Define the message to display while loading
  const loadingMessage = "Loading campaign data...";

  useEffect(() => {
    async function loadCampaignDetails() {
      try {
        setLoading(true);
        setError(null);

        // Fetch campaign details from the API route
        const response = await fetch(`/api/marketing/campaign-data/${campaignId}`);

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.error || `Failed to fetch campaign details: ${response.status}`);
        }

        const details = await response.json();
        setCampaignDetails(details);
      } catch (e: unknown) {
        if (e instanceof Error) {
          setError(e.message);
        } else {
          setError("An unknown error occurred");
        }
        console.error("Error fetching campaign details:", e);
      } finally {
        setLoading(false);
      }
    }

    loadCampaignDetails();
  }, [campaignId]);

  return (
    <SidebarProvider
      style={
        {
          "--sidebar-width": "calc(var(--spacing) * 72)",
          "--header-height": "calc(var(--spacing) * 12)",
        } as React.CSSProperties
      }
    >
      <AppSidebar variant="inset" />
      <SidebarInset>
        <SiteHeader
          breadcrumbRootName="Marketing Dashboard"
          breadcrumbRootPath="/marketing-dashboard"
        />
        <div className="flex flex-1 flex-col">
          <main className="flex flex-1 flex-col gap-6 p-4 md:gap-8 md:p-6">
            {/* Header Section */}
            <div className="space-y-4">
              {/* Back button and breadcrumb */}
              <div className="flex items-center gap-2 mb-4">
                <Link href="/marketing-dashboard">
                  <Button variant="ghost" size="sm" className="gap-2">
                    <ArrowLeft className="h-4 w-4" />
                    Back to Dashboard
                  </Button>
                </Link>
              </div>

              <div className="flex items-center gap-3 min-w-0">
                <Target className="h-8 w-8 text-primary flex-shrink-0" />
                <div className="min-w-0 flex-1">
                  <h1 className="text-3xl font-bold tracking-tight break-words">
                    {loading ? loadingMessage : (campaignDetails?.name ? formatCampaignName(campaignDetails.name) : "Campaign Details")}
                  </h1>
                  {!loading && campaignDetails && (
                    <div className="flex items-center gap-4 mt-2">
                      {campaignDetails.brand_name && (
                        <Badge variant="secondary" className="gap-1">
                          <TrendingUp className="h-3 w-3" />
                          {campaignDetails.brand_name}
                        </Badge>
                      )}
                      {campaignDetails.startDate && campaignDetails.endDate && (
                        <Badge variant="outline" className="gap-1">
                          <Calendar className="h-3 w-3" />
                          {campaignDetails.startDate} - {campaignDetails.endDate}
                        </Badge>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Error State */}
            {error && (
              <div className="p-6 border border-red-200 dark:border-red-800 rounded-lg bg-red-50 dark:bg-red-950/20">
                <div className="flex items-center gap-3">
                  <AlertCircle className="h-5 w-5 text-red-600 dark:text-red-400" />
                  <div>
                    <h3 className="font-semibold text-red-900 dark:text-red-100">Unable to Load Campaign</h3>
                    <p className="text-red-700 dark:text-red-300 mt-1">Could not load campaign details: {error}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Loading State */}
            {loading && (
              <div className="space-y-6">
                {/* Loading Summary Cards */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                  {[...Array(4)].map((_, i) => (
                    <div key={i} className="p-6 border rounded-lg">
                      <div className="flex items-center gap-3 mb-3">
                        <Skeleton className="h-8 w-8 rounded-lg" />
                        <Skeleton className="h-4 w-20" />
                      </div>
                      <Skeleton className="h-8 w-24 mb-2" />
                      <Skeleton className="h-3 w-16" />
                    </div>
                  ))}
                </div>

                {/* Loading Content Grid */}
                <div className="grid gap-6 lg:grid-cols-2">
                  {/* Campaign Info Loading */}
                  <div className="p-6 border rounded-lg">
                    <Skeleton className="h-6 w-40 mb-6" />
                    <div className="grid gap-4 md:grid-cols-2">
                      {[...Array(6)].map((_, i) => (
                        <div key={i} className="space-y-2">
                          <Skeleton className="h-4 w-24" />
                          <Skeleton className="h-5 w-32" />
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Spend Breakdown Loading */}
                  <div className="p-6 border rounded-lg">
                    <Skeleton className="h-6 w-32 mb-6" />
                    <div className="flex justify-center mb-6">
                      <Skeleton className="h-64 w-64 rounded-full" />
                    </div>
                    <div className="space-y-3">
                      {[...Array(4)].map((_, i) => (
                        <div key={i} className="flex justify-between items-center">
                          <Skeleton className="h-4 w-20" />
                          <Skeleton className="h-4 w-16" />
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Campaign Content - Only shown when not loading and no error */}
            {!loading && !error && campaignDetails && (
              <div className="space-y-6">
                {/* KPI Summary Cards */}
                <SummaryCards campaign={campaignDetails} currency={defaultCurrency} />

                {/* Content Grid */}
                <div className="grid gap-6 lg:grid-cols-2">
                  {/* Campaign Information */}
                  <CampaignInfo campaign={campaignDetails} />

                  {/* Spend Breakdown */}
                  <SpendBreakdown campaign={campaignDetails} currency={defaultCurrency} />
                </div>
              </div>
            )}
          </main>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}