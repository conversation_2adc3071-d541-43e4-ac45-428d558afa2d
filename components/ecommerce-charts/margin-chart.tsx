"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON> as Re<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "recharts"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { DollarSign, HelpCircle, Percent } from "lucide-react"
import { MouseEvent, useState } from "react"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { createCompactFormatter, createFormatters } from "./utils"

import { Button } from "@/components/ui/button"
import { CHART_COLORS } from "@/lib/chart-utils"
import { ChartContainer } from "@/components/ui/chart"
import { ChartContextMenu } from "@/components/ui/chart-context-menu"
import type { ChartContextMenuData } from "@/lib/chart-context-menu-utils" // Import type directly
import { ChartWithToggleProps } from "./types"
import { Skeleton } from "@/components/ui/skeleton"

export function Margin<PERSON>hart({ chartData, loading, currency, displayMode, onDisplayModeChange }: ChartWithToggleProps) {
  const { rechartsDateFormatter, rechartsLabelFormatter, customTooltipFormatter } = createFormatters(currency);
  
  // State to track the currently active chart element for context menu
  const [activeElement, setActiveElement] = useState<{
    dataKey?: string
    index?: number
    payload?: Record<string, unknown>
  } | null>(null);

  // State for programmatic context menu control
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [menuPosition, setMenuPosition] = useState({ x: 0, y: 0 });

  // Extract KPI names dynamically based on display mode (still needed for ChartContextMenu logic if it uses it)
  // const kpiNames = displayMode === 'value' // This prop is removed from ChartContextMenu
  //   ? ["Gross Margin", "Contribution Margin"]
  //   : ["% Gross Margin", "% Contribution Margin"];

  // Convert chart data to the format expected by ChartContextMenu
  const contextMenuData: ChartContextMenuData[] = chartData.map((item) => ({
    date: item.date,
    ...Object.fromEntries(
      Object.entries(item).filter(([key]) => key !== 'date')
    )
    // index property might not be needed if activeElement.index is reliable
  })) as ChartContextMenuData[];

  // Handle mouse enter on individual bar elements
  const handleBarMouseEnter = (dataKey: string) => (data: Record<string, unknown>, index: number) => {
    console.log('🐛 [MarginChart] Bar mouse enter:', { dataKey, data, index });
    setActiveElement({
      dataKey,
      index,
      payload: data,
    });
    console.log('🐛 [MarginChart] Active element set:', {
      dataKey,
      index,
      payload: data,
    });
  };

  // Handle mouse leave from chart elements
  const handleMouseLeave = () => {
    console.log('🐛 [MarginChart] Mouse leave - clearing active element');
    setActiveElement(null);
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div className="flex items-center space-x-1">
          <CardTitle>Margin Analysis</CardTitle>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" className="h-6 w-6 p-0">
                  <HelpCircle className="h-4 w-4" />
                  <span className="sr-only">KPI Definition</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p className="max-w-xs">Revenue retained after accounting for product costs (before fulfillment, ads, etc.).</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        <div className="flex items-center space-x-2">
          <Button 
            variant={displayMode === 'value' ? "default" : "outline"} 
            size="icon"
            onClick={() => onDisplayModeChange('value')}
            title="Show Values"
          >
            <DollarSign className="h-4 w-4" />
          </Button>
          <Button 
            variant={displayMode === 'percent' ? "default" : "outline"} 
            size="icon"
            onClick={() => onDisplayModeChange('percent')}
            title="Show Percentages"
          >
            <Percent className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="h-[400px]">
        {loading ? (
          <Skeleton className="h-full w-full" />
        ) : (
          <ChartContainer config={{
            "Gross Margin": { label: "Gross Margin", color: CHART_COLORS.purple },
            "Contribution Margin": { label: "Contribution Margin", color: CHART_COLORS.green },
            "% Gross Margin": { label: "% Gross Margin", color: CHART_COLORS.yellow },
            "% Contribution Margin": { label: "% Contribution Margin", color: CHART_COLORS.orange }
          }}>
            <ChartContextMenu
              chartData={contextMenuData}
              activeElement={activeElement}
              currency={currency}
              isMenuOpen={isMenuOpen}
              onOpenChange={setIsMenuOpen}
              menuPosition={menuPosition}
              // chartType and kpiNames props are removed
            >
              <div
                onContextMenu={(e: MouseEvent) => {
                  console.log('🐛 [MarginChart] Div onContextMenu event:', {
                    clientX: e.clientX,
                    clientY: e.clientY,
                    activeElement: activeElement,
                  });
                  e.preventDefault();
                  setMenuPosition({ x: e.clientX, y: e.clientY });
                  setIsMenuOpen(true);
                }}
              >
                <BarChart
                  data={chartData}
                  margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                  onMouseLeave={handleMouseLeave} // Keep for activeElement
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                  dataKey="date"
                  tickFormatter={rechartsDateFormatter}
                  minTickGap={30}
                />
                <YAxis
                  tickFormatter={createCompactFormatter(displayMode === 'percent')}
                />
                <RechartsTooltip
                  formatter={customTooltipFormatter}
                  labelFormatter={rechartsLabelFormatter}
                />
                <Legend />
                {displayMode === 'value' ? (
                  <>
                    <Bar
                      dataKey="Gross Margin"
                      fill={CHART_COLORS.purple}
                      onMouseEnter={handleBarMouseEnter("Gross Margin")}
                    />
                    <Bar
                      dataKey="Contribution Margin"
                      fill={CHART_COLORS.green}
                      onMouseEnter={handleBarMouseEnter("Contribution Margin")}
                    />
                  </>
                ) : (
                  <>
                    <Bar
                      dataKey="% Gross Margin"
                      fill={CHART_COLORS.yellow}
                      onMouseEnter={handleBarMouseEnter("% Gross Margin")}
                    />
                    <Bar
                      dataKey="% Contribution Margin"
                      fill={CHART_COLORS.orange}
                      onMouseEnter={handleBarMouseEnter("% Contribution Margin")}
                    />
                  </>
                )}
                </BarChart>
              </div>
            </ChartContextMenu>
          </ChartContainer>
        )}
      </CardContent>
    </Card>
  );
}
