"use client"

import { CHART_COLORS, formatCurrency, formatDate, formatNumber } from "@/lib/chart-utils"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "./ui/card"
import {
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts"
import React, { useEffect, useState } from "react" // Removed useMemo

import { Skeleton } from "./ui/skeleton"
import { useCampaignData } from "@/lib/api/campaign-data-service"
import { useFilters } from "@/lib/contexts/filter-context"

// Data structure for the chart
interface TrendChartDataPoint {
  date: string;
  total_spend: number | null;
  conversions: number | null;
  roas: number | null;
}

export function PerformanceTrendChart() {
  const { filters, getQueryParams } = useFilters()
  const queryParamsString = getQueryParams().toString()
  const { data: campaignData, loading, error: apiError } = useCampaignData(queryParamsString)
  
  const [chartData, setChartData] = useState<TrendChartDataPoint[]>([])
  const [error, setError] = useState<string | null>(null)
  const currentCurrency = filters.currency

  // Process campaign data when it's loaded
  useEffect(() => {
    if (loading) {
      // Reset chart data while loading
      setChartData([])
      setError(null)
      return
    }

    if (apiError) {
      console.error("API Error:", apiError)
      setError(apiError)
      setChartData([])
      return
    }

    try {
      if (!Array.isArray(campaignData) || campaignData.length === 0) {
        console.warn("No campaign data returned from API or data is not an array.")
        setChartData([])
        return
      }

      // Process data into TrendChartDataPoint[]
      const processedData = campaignData.map(item => {
        const totalSpend = item.totalSpend ?? 0;
        const conversions = item.totalConversions ?? 0;
        // ROAS might be directly from item.roas or recalculated if necessary
        const roas = item.roas ?? (totalSpend > 0 ? (item.totalConversionValue ?? 0) / totalSpend : 0);
        
        return {
          date: item.date || '',
          total_spend: totalSpend,
          conversions: conversions,
          roas: roas,
        }
      }).sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()); // Ensure data is sorted by date for line chart

      setChartData(processedData)
    } catch (err) {
      console.error("Failed to process trend chart data:", err)
      setError(err instanceof Error ? err.message : "An unknown error occurred")
      setChartData([])
    }
  }, [campaignData, loading, apiError])

  const yAxisTickFormatter = (value: number, axisType: "currency" | "number" | "ratio") => {
    if (axisType === "currency") return formatCurrency(value, currentCurrency); // Removed third argument
    if (axisType === "number") return formatNumber(value);
    if (axisType === "ratio") return `${value.toFixed(2)}x`;
    return value.toString();
  };


  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Performance Trends</CardTitle>
          <CardDescription>Spend, Conversions, and ROAS over time.</CardDescription>
        </CardHeader>
        <CardContent className="h-[300px] lg:h-[400px]">
          <Skeleton className="h-full w-full" />
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Performance Trends</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-red-500">Error loading trend data: {error}</p>
        </CardContent>
      </Card>
    )
  }
  
  if (!chartData.length && !loading) {
     return (
      <Card>
        <CardHeader>
          <CardTitle>Performance Trends</CardTitle>
           <CardDescription>Spend, Conversions, and ROAS over time.</CardDescription>
        </CardHeader>
        <CardContent className="h-[300px] lg:h-[400px] flex items-center justify-center">
          <p>No trend data available for the selected filters.</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Performance Trends</CardTitle>
        <CardDescription>Spend, Conversions, and ROAS over time. (Aggregation: {filters.groupBy})</CardDescription>
      </CardHeader>
      <CardContent className="h-[300px] lg:h-[400px]">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={chartData} margin={{ top: 5, right: 20, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="date" tickFormatter={(value: string) => formatDate(value)} minTickGap={30} />
            <YAxis yAxisId="left" stroke={CHART_COLORS.blue} tickFormatter={(value) => yAxisTickFormatter(value, "currency")} />
            <YAxis yAxisId="right" orientation="right" stroke={CHART_COLORS.green} tickFormatter={(value) => yAxisTickFormatter(value, "number")} />
            <YAxis yAxisId="roas" orientation="right" stroke={CHART_COLORS.orange} tickFormatter={(value) => yAxisTickFormatter(value, "ratio")} domain={[0, 'auto']} hide={true} /> 
            {/* ROAS axis can be hidden if too cluttered, data shown in tooltip */}
            <Tooltip
              formatter={(value: number, name: string) => {
                if (name === "Total Spend") return formatCurrency(value, currentCurrency);
                if (name === "Conversions") return formatNumber(value);
                if (name === "ROAS") return `${value.toFixed(2)}x`;
                return value.toString();
              }}
              labelFormatter={(value: string) => formatDate(value)}
            />
            <Legend />
            <Line yAxisId="left" type="monotone" dataKey="total_spend" name="Total Spend" stroke={CHART_COLORS.blue} activeDot={{ r: 6 }} dot={false}/>
            <Line yAxisId="right" type="monotone" dataKey="conversions" name="Conversions" stroke={CHART_COLORS.green} activeDot={{ r: 6 }} dot={false}/>
            <Line yAxisId="roas" type="monotone" dataKey="roas" name="ROAS" stroke={CHART_COLORS.orange} activeDot={{ r: 6 }} dot={false}/>
          </LineChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  )
}
